const {v4: uuidv4} = require('uuid');
const {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Conductor , <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, MetodoPago, ViajeMetodosPago} = require('../models');
const locationService = require('./location.service');
const websocketServer = require('../websocket/websocket.server');
const firebaseService = require('../notifications/firebase.service');
const {validateCoordinatesDistance, calculateHaversineDistance} = require('./rides.schema');
const {NotFoundError, ValidationError, ConflictError} = require('../utils/errors');
const conductor = require('../models/conductor');


class RidesService{
    constructor(){
        this.TIMEOUT_SECONDS = 300; // tiempo limite para recivir ofertas
        this.MAX_OFFERS = 6; // cantidad de ofertas
        this.SEARCH_RADIUS =  20; // Distancia del radio de busqueda en kmj
    }
  /**
   *  CREAR SOLICITUD DE VIAJE 
   */

  async createRideRequest(userId, rideData){
    try {          
        console.log(`🚗 Creando solicitud de viaje para usuario ${userId}`);
      
        
        // validamos las coordenadas y disntancias
        const distanceKm = validateCoordinatesDistance(
            rideData.origen_lat,
            rideData.origen_lng,
            rideData.destino_lat,
            rideData.destino_lng
        );

        //  verifificamos qeu el usuario no tenga viajes activos
        await this.checkUserActiveRides(userId);

        // luego buscamos conductores cercanos AL PUNTO DE RECOGIDA
        const nearbyDrivers = await locationService.findNearbyDrivers(
            rideData.origen_lat,    // ✅ Coordenadas del punto de recogida
            rideData.origen_lng,    // ✅ Coordenadas del punto de recogida  
            this.SEARCH_RADIUS      // en rango x km
        );
        
        // guardamos el viaje en la BD primero
        const viaje = await this.createViaje(userId, rideData, distanceKm);

        // ⚠️ CASO ESPECIAL: No hay conductores cerca del punto de recogida
        if (nearbyDrivers.length === 0){
            console.log(`⚠️ No hay conductores disponibles cerca del punto de recogida para viaje ${viaje.id}`);
            console.log(`📍 Punto de recogida: ${rideData.origen_lat}, ${rideData.origen_lng}`);
            
            // Cancelamos el viaje inmediatamente
            await viaje.update({
                estado: 'cancelado',
                motivo_cancelacion: 'No hay conductores disponibles cerca del punto de recogida',
                cancelado_por: 'sistema_no_drivers',  // ✅ Reducido a menos de 50 caracteres
                fecha_cancelacion: new Date()
            });

            // Notificamos al usuario via WebSocket sobre la situación
            websocketServer.notifyUser(userId, 'ride:no_drivers_available', {
                viaje_id: viaje.id,
                mensaje: 'No hay conductores disponibles cerca de tu punto de recogida en este momento',
                punto_recogida: {
                    lat: rideData.origen_lat,
                    lng: rideData.origen_lng,
                    direccion: rideData.origen_direccion
                },
                sugerencias: [
                    'Intenta aumentar el precio sugerido para atraer más conductores',
                    'Espera unos minutos e intenta nuevamente',
                    'Verifica que el punto de recogida sea accesible'
                ],
                estado: 'cancelado',
                radio_busqueda_km: this.SEARCH_RADIUS,
                puede_reintentar: true
            });

            // Push notification - DESHABILITADO TEMPORALMENTE
            console.log('🚫 Push notification omitida - Firebase deshabilitado');
            /*
            try {
                await firebaseService.sendToUser(userId, {
                    title: '😔 No hay conductores disponibles',
                    body: 'No encontramos conductores cerca de tu punto de recogida',
                    data: {
                        type: 'no_drivers_available',
                        viaje_id: viaje.id
                    }
                });
            } catch (pushError) {
                console.warn('⚠️ Error enviando push notification:', pushError.message);
            }
            */

            return {
                success: false,
                viaje: {
                    id: viaje.id,
                    estado: 'cancelado',
                    origen: {
                        lat: viaje.origen_lat,
                        lng: viaje.origen_lng,
                        direccion: viaje.origen_direccion
                    },
                    destino: {
                        lat: viaje.destino_lat,
                        lng: viaje.destino_lng,
                        direccion: viaje.destino_direccion
                    },
                    distancia_km: viaje.distancia_km,
                    precio_sugerido: viaje.precio_sugerido,
                    fecha_solicitud: viaje.fecha_solicitud,
                    fecha_cancelacion: new Date(),
                    motivo_cancelacion: 'No hay conductores disponibles cerca del punto de recogida'
                },
                conductores_notificados: 0,
                mensaje: 'No hay conductores disponibles cerca de tu punto de recogida. Puedes intentar nuevamente.',
                radio_busqueda_km: this.SEARCH_RADIUS,
                puede_reintentar: true
            };
        }

        // ✅ Si hay conductores cerca del punto de recogida, procedemos normalmente
        console.log(`✅ Encontrados ${nearbyDrivers.length} conductores cerca del punto de recogida`);
        
        // notificamos a todos los conductores cercanos
        const notificationResult = await this.notifyNearbyDrivers(nearbyDrivers, viaje);

        // tiempo de espera automàtico
        this.setupRideTimeout(viaje.id);

        console.log(`✅ Viaje ${viaje.id} creado, ${notificationResult.notifiedCount} conductores notificados`);
        console.log('ESto es lo que estoy devolviendo del id del Viaje: ', viaje.id )
        return {
            viaje: {
                id:viaje.id,
                estado: viaje.estado,
                origen: {
                    lat: viaje.origen_lat,
                    lng: viaje.origen_lng,
                    direccion: viaje.origen_direccion
                },
                destino: {
                    lat: viaje.destino_lat,
                    lng: viaje.destino_lng,
                    direccion: viaje.destino_direccion
                },
                distancia_km: viaje.distancia_km,
                precio_sugerido: viaje.precio_sugerido,
                fecha_solicitud: viaje.fecha_solicitud
            },
            conductores_notificados: notificationResult.notifiedCount,
            timeout_segundos: this.TIMEOUT_SECONDS
        };
    } catch (error) {
        console.error('❌ Error creando solicitud de viaje:', error.message);
      throw error;
    }
  }
 /**
   * ✅ CREANDO VIAJE y GUARDANDO EN BD
*/
async createViaje(userId, rideData, distanceKm){
    try {
        const viajeId = uuidv4();

        // calculando tiempo estimado (aprox 25 km/h en ciudad) 
        const tiempoEstimado = Math.ceil((distanceKm / 25) * 60) // en minutos

        // calculando tarifa referencial base (opcional puede ser null tambien)
        const tarifaReferencial = this.calculateBaseFare(distanceKm);

        // ✅ OBTENER INFORMACIÓN DEL USUARIO
        const usuario = await Usuario.findByPk(userId, {
            attributes: ['id', 'nombre_completo', 'foto_perfil']
        });

        if (!usuario) {
            throw new NotFoundError('Usuario no encontrado');
        }

        // ✅ CALCULAR PRECIO SUGERIDO SI NO SE PROPORCIONA
        const precioSugerido = rideData.precio_sugerido || this.calculateSuggestedPrice(distanceKm);

        // guardando en la tabla viaje con información del usuario
        const viaje = await Viaje.create({
            id: viajeId,
            usuario_id: userId,
            origen_direccion: rideData.origen_direccion || `${rideData.origen_lat}, ${rideData.origen_lng}`,
            origen_lat: rideData.origen_lat,
            origen_lng: rideData.origen_lng,
            destino_direccion: rideData.destino_direccion || `${rideData.destino_lat}, ${rideData.destino_lng}`,
            destino_lat: rideData.destino_lat,
            destino_lng: rideData.destino_lng,
            distancia_km: distanceKm,
            tiempo_estimado_minutos: tiempoEstimado,
            precio_sugerido: precioSugerido,
            tarifa_referencial: tarifaReferencial,
            // ✅ INFORMACIÓN DEL USUARIO PARA MOSTRAR AL CONDUCTOR
            usuario_nombre: usuario.nombre_completo,
            usuario_foto: usuario.foto_perfil,
            usuario_rating: 0.00, // Por defecto 0, se actualizará con el sistema de rating
            estado: 'solicitado',
            fecha_solicitud: new Date()
        });

        // ✅ GUARDAR MÉTODOS DE PAGO SI SE PROPORCIONAN
        if (rideData.metodos_pago && Array.isArray(rideData.metodos_pago) && rideData.metodos_pago.length > 0) {
            console.log(`💳 Guardando ${rideData.metodos_pago.length} métodos de pago para viaje ${viajeId}`);
            
            // Verificar que los métodos de pago existen
            const metodosValidos = await MetodoPago.findAll({
                where: {
                    id: rideData.metodos_pago
                }
            });

            if (metodosValidos.length > 0) {
                // Crear las relaciones en la tabla intermedia
                const viajeMetodos = metodosValidos.map(metodo => ({
                    viaje_id: viajeId,
                    metodo_pago_id: metodo.id
                }));

                await ViajeMetodosPago.bulkCreate(viajeMetodos);
                console.log(`✅ ${metodosValidos.length} métodos de pago asociados al viaje`);
            }
        }

        return viaje;
        
    } catch (error) {
        console.error('❌ Error creando viaje en BD: ', error.message);
        throw error; 
    }
}

 /**
   *  NOTIFICANDO CONDUCTORES CERCANOS
   */
async notifyNearbyDrivers(drivers, viaje){
    try {
        console.log(`🤙 Notificando ${drivers.length} conductores cercanos`);
        
        let notifiedCount = 0;
        const notifications = [];

        for(const driver of drivers){
            try {

                // datos para la notificaciòn 
                const notificationData = {
                    viaje_id: viaje.id,
                    origen:{
                        lat: viaje.origen_lat,
                        lng: viaje.origen_lng,
                        direccion: viaje.origen_direccion
                    },
                    destino: {
                        lat: viaje.destino_lat,
                        lng: viaje.destino_lng,
                        direccion: viaje.destino_direccion
                    },
                    pasajero: {
                        nombre: viaje.usuario_nombre,
                        foto: viaje.usuario_foto || null,
                        rating: viaje.usuario_rating || 0.0
                    },
                    distancia_km: viaje.distancia_km,
                    precio_sugerido: viaje.precio_sugerido,
                    tiempo_estimado: viaje.tiempo_estimado_minutos,
                    distancia_conductor: driver.distance,
                    timeout_segundos: this.TIMEOUT_SECONDS,
                    timestamp: new Date()
                };


                 // ✅ DEBUG: Verificar que el ID del viaje existe
                console.log('🔍 DEBUG BACKEND - Verificando viaje.id:');
                console.log('   viaje.id:', viaje.id);
                console.log('   typeof viaje.id:', typeof viaje.id);
                console.log('   notificationData.viaje_id:', notificationData.viaje_id);
            
                console.log('ESto es lo qeu estoy enviando al conductor:')
                console.log(JSON.stringify(notificationData ,null, 2));
                
                // lo notificamos via websocket para que le llegue en teimpo real al pasajero
                websocketServer.notifyDriver(driver.conductorId, 'ride:new_request', notificationData);

                // push notificaton si esta fueraa de la app - DESHABILITADO TEMPORALMENTE
                console.log(`🚫 Push notification omitida para conductor ${driver.conductorId} - Firebase deshabilitado`);
                const pushResult = { success: true, disabled: true }; // Simular respuesta exitosa
                /*
                const pushResult = await firebaseService.sendToDriver(driver.conductorId, {
                    title: ' 😮‍💨 Nueva solicitud de viaje',
                    body: viaje.precio_sugerido 
                    ? `Viaje por S/. ${viaje.precio_sugerido} - ${Number(viaje.distancia_km).toFixed(1)} km`
                    : `Nuevo viaje - ${Number(viaje.distancia_km).toFixed(1)} km`,
                    data: {
                        type: 'new_ride_request',
                        viaje_id: viaje.id,
                        temeout: this.TIMEOUT_SECONDS.toString()
                    }
                });
                */

                notifications.push({
                    conductorId: driver.conductorId,
                    WebSocket: true,
                    push: pushResult.success,
                    distance: driver.distance
                });
                notifiedCount++;
                
            } catch (error) {
                console.error(`❌ Error notificando conductor ${driver.conductorId}: `, error.message);
                throw error; 
 
            }
        }
        return{
            notifiedCount,
            totalDrivers: drivers.length,
            notifications
        };
        
    } catch (error) {
        console.error('❌ Error en notificaciòn masiva: ', error.message);
        throw error; 
    }
}
  /**
   * ✅ ACEPTA OFERTA DE CONDUCTOR por primera primera vez
   */
async acceptOriginalRidePrice(viajeId, conductorId, acceptanceData = {}){
    try{
        console.log(`🚕 Conductor ${conductorId} acepta el precio original para el viaje ${viajeId} `);

        const {mensaje, tiempo_estimado_llegada} = acceptanceData;

        //  verificamos que el viaje existe y esta disponible
        const viaje = await Viaje.findByPk(viajeId, {
            include: [{
                model: Usuario,
                as: 'pasajero',
                attributes: ['id', 'nombre_completo', 'telefono']
            }]
        });

        if(!viaje){
            throw new NotFoundError('Viaje no econtrado');
        }

        // verificamos estado del viaje que  este en solicitado
        if (!['solicitado', 'ofertas_recividas'].includes(viaje.estado)){
            throw new ConflictError(`El viaje està en estado ${viaje.estado}`);
        }

        // Verificamos el conductor que acepta no haya viajes para este viaje
        const existingOffer = await OfertaViaje.findOne({
            where: {
                viaje_id: viajeId,
                conductor_id: conductorId
            }
        });

        if(existingOffer){
            throw new ConflictError("El conductor ya oferto para este viaje"); 
        }

        // verificamos si el conductor actual esta diponible
        const conductor = await Conductor.findByPk(conductorId, {
            include: [{
                model: Vehiculo,
                as: 'vehiculos',
                where: {activo: true},
                required: false
            }]
        });

        if(!conductor ||  conductor.estado !== 'activo' || !conductor.disponible){
            throw new ValidationError('El conductro actual no se encuentra disponible');
        }

        // calculamos el tiempo de llegado sino se proporciona
        const tiempoLlegada = tiempo_estimado_llegada || this.calculateArrivalTime(
            conductor.ubicacion_lat,
            conductor.ubicacion_lng,
            viaje.origen_lat,
            viaje.origen_lng
        );

        // usamos transacion para que toddo los datos de guarden correctamente
        const transaction = await Viaje.sequelize.transaction();

        try{
            // creamos la oferta acetada, que simulara como si fuera que el conducto ofertò
            // con los mismo precios
            const oferta = await OfertaViaje.create({
                id: uuidv4(),
                viaje_id: viajeId,
                conductor_id: conductorId,
                tarifa_propuesta: viaje.precio_sugerido,
                tiempo_estimado_llegada_minutos: tiempoLlegada,
                mensaje: mensaje || `Acepto tu precio de S/. ${viaje.precio_sugerido}`,
                estado: 'aceptada',
                fecha_oferta: new Date(),
                fecha_aceptacion: new Date()
            }, {transaction});

            await viaje.update({
                estado: 'aceptado',
                conductor_id: conductorId,
                vehiculo_id: conductor.vehiculos?.[0]?.id || null,
                tarifa_acordada: viaje.precio_sugerido,
                fecha_aceptacion: new Date()
            }, {transaction})

            // actulizamos los demas ofertas pendientes como rechazadas 

            await OfertaViaje.update(
                {
                    estado: 'rechazada',
                    fecha_rechazo: new Date()
                },
                {
                    where: {
                        viaje_id: viajeId,
                        id: {[require('sequelize').Op.ne]: oferta.id},
                        estado: 'pendiente'
                    },
                    transaction
                }
            );
            await transaction.commit();

            // 📣 notificaciones
            try {

                const passengerNotificationData = {
                    viaje_id: viajeId,
                    oferta_id: oferta.id,
                    tipo: 'precio_original_aceptado',
                    conductor: {
                        id: conductor.id,
                        nombre: conductor.nombre_completo,
                        telefono: conductor.telefono,
                        vehiculo: conductor.vehiculos?.[0] || null
                    },
                    tarifa_acordada : viaje.precio_sugerido,
                    tiempo_llegada: tiempoLlegada,
                    mensaje: mensaje || `${conductor.nombre_completo} aceptò tu precio original`,
                    origen: {
                        lat: viaje.origen_lat,
                        lng: viaje.origen_lng
                    },
                    destino: {
                        lat: viaje.destino_lat,
                        lng: viaje.destino_lng,
                        direccion: viaje.destino_direccion
                    }
                };

                // enviamo la data por websocket al pasajero
                websocketServer.notifyPriceAcceptedImmediately(viaje.usuario_id, passengerNotificationData);

                // push notificaton al pasjero
                await firebaseService.sendToUser(viaje.usuario_id, {
                    title: '😏 Conducto asignado!',
                    body: `${conductor.nombre_completo} aceptò tu precio S/.${viaje.precio_sugerido} `,
                    data: {
                        type: 'price_accepted_inmediately',
                        viaje_id: viajeId,
                        conductor_id: conductorId
                    }
                });

                // notificamos a otros conductores que se perdieron el viaje
                const rejectedOffers = await OfertaViaje.findAll({
                    where: {
                        viaje_id: viajeId,
                        estado: 'rechazada'
                    },
                    attributes: ['conductor_id']
                });

                const rejectedConductorIds = rejectedOffers
                        .map(offer => offer.conductor_id)
                        .filter(id => id !== conductorId);
                if(rejectedConductorIds.length > 0){
                    websocketServer.notifyRideTakenByOther(rejectedConductorIds, {
                        viaje_id: viajeId,
                        mensaje: 'Otro conductro aceptò el precio original',
                        conductor_ganador: conductor.nombre_completo
                    })
                }
                       
            } catch (notificationError) {
                console.warn('☣️ Erro en notificacones para aceptar la primera oferta: ', notificationError.message);                
            }

            console.log( `✅ Precio original S/. ${viaje.precio_sugerido} aceptado por conductor ${conductorId} `);
            return {
                viaje: {
                    id: viaje.id,
                    estado: 'aceptado',
                    tarifa_acordada:  viaje.precio_sugerido,
                    tiempo_llegada: tiempoLlegada,
                    estado: 'aceptado'
                },
                oferta:{
                    id: oferta.id,
                    tarifa_propuesta: viaje.precio_sugerido,
                    tiempo_llegada: tiempoLlegada,
                    estado: 'aceptada'
                },
                conductor: {
                    id: conductor.id,
                    nombre: conductor.nombre_completo,
                    telefono: conductor.telefono,
                    vehiculo: conductor.vehiculos?.[0] || null
                },
                pasajero: 'Precio original aceptado, Viaje confirmado',
                siguiente_paso: 'Dirigirse al putno de recogida'
            };


        }catch(error){
           await transaction.rollback();
           throw error; 
        }


    }catch(error){
        console.error('❌ Error acpetadno el preico original', error.message);
        throw error;
        
    }
}
 /**
   * ✅ CREAR OFERTA DE CONDUCTOR por primera primera vez
   */
async createOffer(conductorId, viajeId, offerData){
    try {
        console.log(`🏷️ Neuva oferta de viaje de ${conductorId}  para viaje: ${viajeId}`);
        
        // nos aseguramos el viaje enviado existe y tiene un estado correcto
        const viaje = await Viaje.findByPk(viajeId, {
            include: [{model: Usuario, as: 'pasajero'}]
        });

        if(!viaje){
            throw new NotFoundError('Viaje no econtrado'); 
        }
        if(!['solicitado', 'ofertas_recibidas'].includes(viaje.estado)){
            throw new ConflictError(`El viaje se ecuenra ${viaje.estado}, ya no esta diponible para ofertas`); 
        }

        // verificamos el estado del viaje como el  limite de ofertas
        const existingOffers = await OfertaViaje.count({
            where: {viaje_id: viajeId, estado: 'pendiente'}
        });

        if(existingOffers >= this.MAX_OFFERS){
            throw new ConflictError("☢️ Ya no se pude craar un nuevo viajes, ya que llegò al limite de ofertar por viaje"); 
        }

        // no asegurajmos de que el conductor no haya  ofertado antes
        const existingOffer = await OfertaViaje.findOne({
            where: {
                viaje_id: viajeId,
                conductor_id: conductorId
            }
        });
        if(existingOffer){
            throw new ConflictError('⛔️ El conductro ya ha ofertado para este viaje');
        }
        
        // obtenemos los datos del conductor
        const conductor = await Conductor.findByPk(conductorId, {
            include: [{model:Vehiculo, as: 'vehiculos', where: {activo: true}, required: false}]
        });
        if(!conductor || conductor.estado !== 'activo' || !conductor.disponible  ){
            throw new ValidationError("Conductor no disponible para ofertar"); 
        }
        
        // calculamo el tiempo estimado de llegada a la ubicacion del pasajero
        const tiempoLlegada = this.calculateArrivalTime(
                conductor.ubicacion_lat,
                conductor.ubicacion_lng,
                viaje.origen_lat,
                viaje.origen_lng
        );

        // ⭐️ Creamos la primera oferta
        const fechaExpiracion = new Date();
        fechaExpiracion.setMinutes(fechaExpiracion.getMinutes() + 8) // solo abrà 8 min para que acepte el conducto sino le enviarà que no tiene conductores disponible en su zona

        const oferta = await OfertaViaje.create({
            id: uuidv4(),
            viaje_id: viajeId,
            conductor_id: conductorId,
            tarifa_propuesta: offerData.tarifa_propuesta,
            tiempo_estimado_llegada_minutos: tiempoLlegada,
            mensaje: offerData.mensaje || null,
            fecha_expiracion: fechaExpiracion,
            estado: 'pendiente'
        })

        // notificamos al pasajero de la nueva oferta de algun conductor cercano
        const notificationData = {
            oferta_id: oferta.id,
            viaje_id: viajeId,
            conductor: {
                id: conductor.id,
                nombre: conductor.nombre_completo,
                telefono: conductor.telefono,
                Vehiculo: conductor.vehiculos?.[0] || null 
            },
            tarifa_propuesta: oferta.tarifa_propuesta,
            tiempo_llegada: tiempoLlegada,
            mensaje: oferta.mensaje,
            expira_en: 300 // esto son 5 minutos
        };

        // para notificacoin utlizamos websocker del pasajero
        websocketServer.notifyUser(viaje.usuario_id, 'ride:offer_received', notificationData);

        // tambine le envimos un push notification con firebase - DESHABILITADO TEMPORALMENTE
        console.log(`🚫 Push notification omitida para usuario ${viaje.usuario_id} - Firebase deshabilitado`);
        /*
        await firebaseService.sendToUser(viaje.usuario_id, {
            title: '🕊️ Tu oferta fue aceptada',
            body: `${conductor.nombre_completo} ofrece S/. ${oferta.tarifa_propuesta} - Llega en ${tiempoLlegada} min`,
            data: {
                type: 'offer_received',
                viaje_id: viajeId,
                oferta_id: oferta.id
            }
        });
        */
    
        // Actualizamos la oferta del viaje si es la primera oferta
        if(existingOffers === 0){
            await viaje.update({estado: 'ofertas_recibidas'});
        }

        console.log( `✅ Oferta ${oferta.id}  creada y notificada al pasajero`);

        return {
            oferta: {
                id:oferta.id,
                tarifa_propuesta: oferta.tarifa_propuesta,
                tiempo_llegada: oferta.tiempoLlegada,
                mensaje: oferta.mensaje,
                fecha_oferta: oferta.fecha_oferta,
                estado: oferta.estado
            },
            conductor: {
                id: conductor.id,
                nombre: conductor.nombre_completo
            }
        };

    } catch (error) {
        console.error('❌ Error, el conductor no pudo crear una oferta: ', error.message);
        throw error;
    }
  }
   /**
   *  EL PASAJERO ACEPTA LA OFERTA despues de la primera oferta del conductor
   */
async acceptOffer(rideId, offerId, userId) {
    try {
        console.log(`🎯 Aceptando oferta: rideId=${rideId}, offerId=${offerId}, userId=${userId}`);

        // ✅ VALIDAR PARÁMETROS PRIMERO
        if (!rideId || !offerId || !userId) {
            throw new Error(`Parámetros faltantes: rideId=${rideId}, offerId=${offerId}, userId=${userId}`);
        }

        // 1. Buscar viaje
        const viaje = await Viaje.findOne({
            where: { id: rideId, usuario_id: userId },
            include: [{ model: Usuario, as: 'pasajero' }]
        });

        console.log('✅ Viaje encontrado:', viaje ? `${viaje.id} - Estado: ${viaje.estado}` : 'NO ENCONTRADO');

        if (!viaje) {
            throw new NotFoundError('Viaje no encontrado o no autorizado');
        }

        // 2. Verificar estado del viaje
        if (!['solicitado', 'ofertas_recibidas'].includes(viaje.estado)) {
            throw new ConflictError("El viaje ya no está disponible");
        }

        // 3. Buscar oferta
        const oferta = await OfertaViaje.findOne({
            where: { 
                id: offerId, 
                viaje_id: rideId, 
                estado: 'pendiente' 
            },
            include: [{
                model: Conductor,
                as: 'conductor',
                include: [{
                    model: Vehiculo, 
                    as: 'vehiculos', 
                    where: { activo: true }, 
                    required: false
                }]
            }]
        });

        if (!oferta) {
            throw new NotFoundError('🫙 No se encontraron ninguna oferta o ya no está disponible');
        }

        // 4. Verificar expiración
        if (new Date() > oferta.fecha_expiracion) {
            await oferta.update({ estado: 'expirada' });
            throw new ConflictError("La oferta ha expirado");
        }

        // ✅ 5. USAR TRANSACCIÓN CORRECTAMENTE - UNA SOLA VEZ
        const transaction = await Viaje.sequelize.transaction();

        try {
            // Actualizar viaje (SOLO UNA VEZ)
            await viaje.update({
                estado: 'aceptado',
                conductor_id: oferta.conductor_id,
                vehiculo_id: oferta.conductor.vehiculos?.[0]?.id || null,
                tarifa_acordada: oferta.tarifa_propuesta,
                fecha_aceptacion: new Date()
            }, { transaction });

            // Aceptar la oferta seleccionada
            await oferta.update({ 
                estado: 'aceptada',
                fecha_aceptacion: new Date()
            }, { transaction });

            // Rechazar todas las demás ofertas
            await OfertaViaje.update(
                { 
                    estado: 'rechazada',
                    fecha_rechazo: new Date()
                },
                {
                    where: {
                        viaje_id: rideId,
                        id: { [require('sequelize').Op.ne]: offerId },
                        estado: 'pendiente'
                    },
                    transaction
                }
            );

            // ✅ COMMIT CON PUNTO Y COMA
            await transaction.commit();

        } catch (transactionError) {
            await transaction.rollback();
            throw transactionError;
        }

        // 6. Notificaciones (fuera de la transacción)
        try {
            // Notificar al conductor aceptado
            websocketServer.notifyDriver(oferta.conductor_id, 'ride:offer_accepted', {
                viaje_id: rideId,
                oferta_id: offerId,
                pasajero: {
                    nombre: viaje.pasajero.nombre_completo,
                    telefono: viaje.pasajero.telefono
                },
                origen: {
                    lat: viaje.origen_lat,
                    lng: viaje.origen_lng,
                    direccion: viaje.origen_direccion
                },
                destino: {
                    lat: viaje.destino_lat,
                    lng: viaje.destino_lng,
                    direccion: viaje.destino_direccion
                },
                tarifa_acordada: oferta.tarifa_propuesta
            });

            // Notificar a conductores rechazadas
            const rejectedOffers = await OfertaViaje.findAll({
                where: {
                    viaje_id: rideId,
                    estado: 'rechazada'
                }
            });

            for (const rejectedOffer of rejectedOffers) {
                websocketServer.notifyDriver(rejectedOffer.conductor_id, 'ride:offer_rejected', {
                    viaje_id: rideId,
                    oferta_id: rejectedOffer.id,
                    mensaje: 'El pasajero ya seleccionó otra oferta'
                });
            }

            // Push notifications - DESHABILITADO TEMPORALMENTE
            console.log(`🚫 Push notification omitida para conductor ${oferta.conductor_id} - Firebase deshabilitado`);
            /*
            await firebaseService.sendToDriver(oferta.conductor_id, {
                title: '🎊 Oferta aceptada!!!',
                body: `Tu oferta de S/. ${oferta.tarifa_propuesta} fue aceptada`,
                data: {
                    type: 'offer_accepted',
                    viaje_id: rideId
                }
            });
            */

        } catch (notificationError) {
            console.warn('⚠️ Error en notificaciones:', notificationError.message);
        }

        console.log(`✅ Oferta ${offerId} aceptada, viaje ${rideId} iniciado`);
        
        return {
            viaje: {
                id: viaje.id,
                estado: 'aceptado',
                tarifa_acordada: oferta.tarifa_propuesta,
                fecha_aceptacion: new Date()
            },
            conductor: {
                id: oferta.conductor.id,
                nombre: oferta.conductor.nombre_completo,
                telefono: oferta.conductor.telefono,
                vehiculo: oferta.conductor.vehiculos?.[0] || null
            },
            siguiente_paso: 'El conductor se dirige a ti'
        };

    } catch (error) {
        console.error('❌ Error, el pasajero no pudo aceptar la oferta:', error.message);
        throw error;
    }
}
     /**
   *  OBTENER OFERTAS DE UN VIAJE  como pasajero🚀
   */

async getRideOffers(viajeId, userId){
        try {
            const viaje = await Viaje.findOne({
                where: {id:viajeId, usuario_id: userId}
            });

            if(!viaje){
                throw new NotFoundError('Viaje o econtrado para el usuario actual');
            };

            //Obtener ofetas con informacion del conductor

            const ofertas = await OfertaViaje.findAll({
                where: {viaje_id: viajeId},
                include: [{
                    model:Conductor,
                    as: 'conductor',
                    attributes: ['id', 'nombre_completo', 'telefono'],
                    include: [{
                        model: Vehiculo,
                        as: 'vehiculos',
                        where: {activo: true},
                        required: false,
                        attributes: ['placa', 'foto_lateral']
                    }]
                }],
                order: [['fecha_oferta', 'ASC']]
            });

            return ofertas.map(
                oferta => ({
                    id: oferta.id,
                    tarifa_propuesta: oferta.tarifa_propuesta,
                    tiempo_estimado_llegada: oferta.tiempo_estimado_llegada_minutos,
                    mensaje: oferta.mensaje,
                    estado: oferta.estado,
                    fecha_oferta: oferta.fecha_oferta,
                    conductor: {
                        id: oferta.conductor.id,
                        nombre: oferta.conductor.nombre_completo,
                        telefono: oferta.conductor.telefono,
                        vehiculo: oferta.conductor.vehiculos?.[0] || null
                    }
                })); 
        } catch (error) {
            console.error('❌ Error obteniendo ofertas: ', error.message);            
        }
     }
/**
 * ✅ OBTENER SOLICITUDES CERCANAS PARA CONDUCTOR CON INFORMACIÓN COMPLETA
 */
async getNearbyRequests(conductorId, conductorLat, conductorLng) {
  try {
    console.log(`🔍 Buscando solicitudes cercanas para conductor ${conductorId}`);

    // Buscar viajes en estado 'solicitado' o 'ofertas_recibidas' cerca del conductor
    const { calculateHaversineDistance } = require('./rides.schema');

    const viajes = await Viaje.findAll({
      where: {
        estado: ['solicitado', 'ofertas_recibidas']
      },
      include: [
        {
          model: Usuario,
          as: 'pasajero',
          attributes: ['id', 'nombre_completo', 'telefono', 'foto_perfil']
        },
        {
          model: OfertaViaje,
          as: 'ofertas',
          where: { conductor_id: conductorId },
          required: false // LEFT JOIN para ver si ya ofertó
        },
        {
          // ✅ INCLUIR MÉTODOS DE PAGO
          model: MetodoPago,
          as: 'metodosPago',
          through: { attributes: [] }, // No incluir campos de la tabla intermedia
          attributes: ['id', 'nombre', 'tipo']
        }
      ],
      order: [['fecha_solicitud', 'DESC']],
      limit: 10
    });

    // Filtrar por distancia y calcular tiempo de llegada
    const nearbyRequests = [];

    for (const viaje of viajes) {
      const distance = calculateHaversineDistance(
        conductorLat,
        conductorLng,
        viaje.origen_lat,
        viaje.origen_lng
      );

      // Solo mostrar viajes dentro del radio de búsqueda
      if (distance <= this.SEARCH_RADIUS) {
        const tiempoLlegada = this.calculateArrivalTime(
          conductorLat,
          conductorLng,
          viaje.origen_lat,
          viaje.origen_lng
        );

        // ✅ PREPARAR MÉTODOS DE PAGO
        const metodosPago = viaje.metodosPago ? viaje.metodosPago.map(metodo => metodo.nombre) : [];

        nearbyRequests.push({
          viaje_id: viaje.id,
          // ✅ INFORMACIÓN COMPLETA DEL USUARIO
          usuario: {
            id: viaje.pasajero.id,
            nombre: viaje.usuario_nombre || viaje.pasajero.nombre_completo,
            telefono: viaje.pasajero.telefono,
            foto: viaje.usuario_foto || viaje.pasajero.foto_perfil,
            rating: viaje.usuario_rating || 0.0
          },
          origen: {
            lat: viaje.origen_lat,
            lng: viaje.origen_lng,
            direccion: viaje.origen_direccion
          },
          destino: {
            lat: viaje.destino_lat,
            lng: viaje.destino_lng,
            direccion: viaje.destino_direccion
          },
          distancia_km: viaje.distancia_km,
          distancia_conductor: distance,
          tiempo_llegada_estimado: tiempoLlegada,
          // ✅ PRECIOS COMPLETOS
          precio_usuario: viaje.precio_sugerido, // Lo que pide el usuario
          precio_sugerido_app: viaje.tarifa_referencial, // Lo que sugiere la app
          // ✅ MÉTODOS DE PAGO
          metodos_pago: metodosPago,
          fecha_solicitud: viaje.fecha_solicitud,
          ya_oferté: viaje.ofertas && viaje.ofertas.length > 0,
          total_ofertas: await OfertaViaje.count({
            where: { viaje_id: viaje.id, estado: 'pendiente' }
          }),
          // ✅ COORDENADAS PARA USO INTERNO
          coordenadas: {
            origen: { lat: viaje.origen_lat, lng: viaje.origen_lng },
            destino: { lat: viaje.destino_lat, lng: viaje.destino_lng }
          }
        });
      }
    }

    // Ordenar por distancia del conductor
    nearbyRequests.sort((a, b) => a.distancia_conductor - b.distancia_conductor);

    console.log(`✅ Encontradas ${nearbyRequests.length} solicitudes cercanas para conductor ${conductorId}`);
    return nearbyRequests;

  } catch (error) {
    console.error('❌ Error obteniendo solicitudes cercanas:', error.message);
    throw error;
  }
}

/**
 * ✅ OBTENER OFERTAS DEL CONDUCTOR
 */
async getDriverOffers(conductorId, options = {}) {
  try {
    const { estado, limit = 20, offset = 0 } = options;

    console.log(`📋 Obteniendo ofertas del conductor ${conductorId}`);

    const whereCondition = { conductor_id: conductorId };
    if (estado && estado !== 'todos') {
      whereCondition.estado = estado;
    }

    const { count, rows } = await OfertaViaje.findAndCountAll({
      where: whereCondition,
      include: [
        {
          model: Viaje,
          as: 'viaje',
          attributes: [
            'id', 'origen_direccion', 'destino_direccion',
            'distancia_km', 'estado', 'fecha_solicitud'
          ],
          include: [
            {
              model: Usuario,
              as: 'pasajero',
              attributes: ['nombre_completo', 'telefono']
            }
          ]
        }
      ],
      order: [['fecha_oferta', 'DESC']],
      limit,
      offset
    });

    const offers = rows.map(oferta => ({
      id: oferta.id,
      viaje_id: oferta.viaje_id,
      tarifa_propuesta: oferta.tarifa_propuesta,
      tiempo_estimado_llegada: oferta.tiempo_estimado_llegada_minutos,
      mensaje: oferta.mensaje,
      estado: oferta.estado,
      fecha_oferta: oferta.fecha_oferta,
      visto_por_usuario: oferta.visto_por_usuario,
      viaje: {
        id: oferta.viaje.id,
        origen_direccion: oferta.viaje.origen_direccion,
        destino_direccion: oferta.viaje.destino_direccion,
        distancia_km: oferta.viaje.distancia_km,
        estado: oferta.viaje.estado,
        fecha_solicitud: oferta.viaje.fecha_solicitud,
        pasajero: {
          nombre: oferta.viaje.pasajero.nombre_completo,
          telefono: oferta.viaje.pasajero.telefono
        }
      }
    }));

    return {
      data: offers,
      total: count
    };

  } catch (error) {
    console.error('❌ Error obteniendo ofertas del conductor:', error.message);
    throw error;
  }
}

/**
 * ✅ OBTENER HISTORIAL DE VIAJES COMPLETADOS DEL CONDUCTOR (VERSIÓN SIMPLIFICADA)
 */
async getDriverRideHistory(conductorId, options = {}) {
  try {
    const {
      estado = 'completado',
      limit = 20,
      offset = 0
    } = options;

    console.log(`📋 Obteniendo historial de viajes del conductor ${conductorId}`);

    // Construir condiciones WHERE básicas
    const whereCondition = {
      conductor_id: conductorId
    };

    // Filtrar por estado (simplificado)
    if (estado && estado !== 'todos') {
      whereCondition.estado = estado;
    } else {
      // Por defecto, mostrar solo viajes completados
      whereCondition.estado = 'completado';
    }

    const { count, rows } = await Viaje.findAndCountAll({
      where: whereCondition,
      include: [
        {
          model: Usuario,
          as: 'pasajero',
          attributes: ['id', 'nombre_completo', 'telefono', 'foto_perfil'],
          required: false // LEFT JOIN para evitar errores si no hay pasajero
        }
      ],
      order: [['fecha_solicitud', 'DESC']],
      limit,
      offset
    });

    // Formatear los datos del historial (versión simplificada)
    const viajes = rows.map(viaje => ({
      id: viaje.id,
      estado: viaje.estado,
      fecha_solicitud: viaje.fecha_solicitud,
      fecha_aceptacion: viaje.fecha_aceptacion,
      fecha_finalizacion: viaje.fecha_finalizacion,

      // Información básica del viaje
      origen_direccion: viaje.origen_direccion,
      destino_direccion: viaje.destino_direccion,
      distancia_km: viaje.distancia_km,

      // Información financiera básica
      precio_sugerido: viaje.precio_sugerido,
      tarifa_acordada: viaje.tarifa_acordada,

      // Información del pasajero (con validación)
      pasajero: viaje.pasajero ? {
        id: viaje.pasajero.id,
        nombre: viaje.pasajero.nombre_completo || 'Usuario',
        telefono: viaje.pasajero.telefono || 'No disponible'
      } : {
        id: null,
        nombre: 'Usuario no disponible',
        telefono: 'No disponible'
      },

      // Información adicional
      motivo_cancelacion: viaje.motivo_cancelacion,
      cancelado_por: viaje.cancelado_por
    }));

    // Estadísticas básicas y simples
    const estadisticas = {
      total_viajes_en_consulta: count,
      viajes_mostrados: viajes.length
    };

    return {
      data: viajes,
      total: count,
      estadisticas
    };

  } catch (error) {
    console.error('❌ Error obteniendo historial de viajes del conductor:', error.message);
    throw error;
  }
}

/**
* ✅ CANCELAR VIAJE (cambiar estado a cancelado, no eliminar)
*/
async cancelRide(rideId, userId, motivo =  "Pasajero cancelo el viaje"){
    try{
        console.log(`☢️ Cancelado el viaje: ${rideId} para usuario ${userId}`)

        // Buscamso los viajes relacionados al usuario
        const viaje = await Viaje.findOne({
            where: {
                id: rideId,
                usuario_id: userId
            },
            include: [
                {
                    model: Usuario,
                    as: 'pasajero',
                    attributes: ['id', 'nombre_completo', 'telefono']
                }
            ]
        });

        if(!viaje){
            throw new NotFoundError('No hay viajes que cancelar par este usuaio');
         }

         // Verificamos los estados de los viaje para para poder cancelar
         // acà tengo una duda, si devemo dejar que cancele cuando el viaje esta en estado acpetado,
         // lo voy a dejar para que se pueda cancelar cuando sea aceptado ☢️
         const estadosPermitidosCancelacion = ['solicitado', 'ofertas_recibidas', 'aceptado'];
         if(!estadosPermitidosCancelacion.includes(viaje.estado)){
            throw new ConflictError(`No se puedde cancelar el vijae en estado: ${viaje.estado}`);
         }

         // usamos trasacciones para asegurarno que lso dato se actualen o sino ninguno lo harà, una forma de tener un rollback a la hora de trabaar con BD
         const transaction = await Viaje.sequelize.transaction();

         try {
            await viaje.update({
                estado: 'cancelado',
                motivo_cancelacion: motivo,
                cancelado_por: 'pasajero',
                fecha_cancelacion: new Date()
            }, {transaction}) ;

            // Tambien acutlizamos la tabla de OfertaViaje, en caso de que el viaje ya habrì recivido ofertas
            await OfertaViaje.update(
                {
                    estado: 'cancelado_por_viaje',
                    fecha_rechazo: new Date()
                },
                {
                    where: {
                        viaje_id: rideId,
                        estado: 'pendiente'
                    },
                    transaction
                }
            );

            // Si habìan conductores que aceptaron para este viaje, entonces los notificaremos
            if(viaje.conductor_id){
                const conductor = await Conductor.findByPk(viaje.conductor_id, {
                    attributes: ['id', 'nombre_completo', 'telefono']
                });

                if(conductor){
                    // notificamos al conductor por ws
                    websocketServer.notifyDriver(viaje.conductor_id, 'ride:cancelled_by_passenger', {
                        viaje_id: rideId,
                        motivo: motivo,
                        pasjaero: {
                            nombre: viaje.pasajero.nombre_completo
                        },
                        mensaje: `El pasaajero ${viaje.pasajero.nombre_completo} cancelò el viaje`
                    });

                    console.log(` Conductor ${viaje.conductor_id} notificado de la cancelaciòn `);
                    
                }
            }


            // notificamos a los conductores que tenìan oferta pendientes
            const ofertas = await OfertaViaje.findAll({
                where: {
                    viaje_id: rideId,
                    estado: 'cancelado_por_viaje'
                },
                attributes: ['conductor_id'],
                transaction
            });
            
        for (const oferta of ofertas) {
          websocketServer.notifyDriver(oferta.conductor_id, 'ride:cancelled_by_passenger', {
            viaje_id: rideId,
            motivo: motivo,
            mensaje: 'El viaje fue cancelado por el pasajero'
          });
        }

        // ✅ NOTIFICAR A TODOS LOS CONDUCTORES CERCANOS QUE VEÍAN ESTA SOLICITUD
        // Buscar conductores que estaban en el radio de búsqueda de esta solicitud
        const nearbyDrivers = await locationService.findNearbyDrivers(
            viaje.origen_lat,
            viaje.origen_lng,
            this.SEARCH_RADIUS
        );

        console.log(`📢 Notificando cancelación a ${nearbyDrivers.length} conductores cercanos`);

        // Notificar a todos los conductores cercanos para que actualicen su dashboard
        for (const driver of nearbyDrivers) {
            try {
                websocketServer.notifyDriver(driver.conductorId, 'ride:cancelled_remove_from_dashboard', {
                    viaje_id: rideId,
                    motivo: motivo,
                    mensaje: 'Esta solicitud fue cancelada por el pasajero',
                    action: 'remove_from_list' // Acción específica para el frontend
                });
            } catch (notificationError) {
                console.warn(`⚠️ Error notificando conductor ${driver.conductorId}:`, notificationError.message);
            }
        }
        

        await transaction.commit();

        console.log(`✅ Viaje ${rideId} cancelado exitosamente. Motivo: ${motivo}`);

        return {
          viaje: {
            id: viaje.id,
            estado: 'cancelado',
            motivo_cancelacion: motivo,
            cancelado_por: 'pasajero',
            fecha_cancelacion: new Date(),
            origen: {
              lat: viaje.origen_lat,
              lng: viaje.origen_lng,
              direccion: viaje.origen_direccion
            },
            destino: {
              lat: viaje.destino_lat,
              lng: viaje.destino_lng,
              direccion: viaje.destino_direccion
            }
          },
          ofertas_canceladas: ofertas.length,
          conductor_notificado: !!viaje.conductor_id,
          mensaje: 'Viaje cancelado correctamente'
        };



         } catch (transactionError) {
            await transaction.rollback();
            throw transactionError; 
         }

    }catch(error){
        console.error('❌ Error cancelando el viaje: ', error.message);
        throw error;
        
    }
}

/**
* ✅ RECHAZAR OFERTA ESPECÍFICA (pasajero rechaza una oferta de conductor)
*/
  async rejectOffer(rideId, offerId, userId) {
    try {
      console.log(`❌ Rechazando oferta ${offerId} para viaje ${rideId}`);

      // 1. Verificar que el viaje pertenece al usuario
      const viaje = await Viaje.findOne({
        where: {
          id: rideId,
          usuario_id: userId
        }
      });

      if (!viaje) {
        throw new NotFoundError('Viaje no encontrado o no autorizado');
      }

      // 2. Buscar la oferta específica
      const oferta = await OfertaViaje.findOne({
        where: {
          id: offerId,
          viaje_id: rideId,
          estado: 'pendiente'
        },
        include: [{
          model: Conductor,
          as: 'conductor',
          attributes: ['id', 'nombre_completo']
        }]
      });

      if (!oferta) {
        throw new NotFoundError('Oferta no encontrada o ya no está disponible');
      }

      // 3. Actualizar la oferta a rechazada
      await oferta.update({
        estado: 'rechazada',
        fecha_rechazo: new Date()
      });

      // 4. Notificar al conductor del rechazo
      websocketServer.notifyDriver(oferta.conductor_id, 'ride:offer_rejected', {
        viaje_id: rideId,
        oferta_id: offerId,
        mensaje: 'El pasajero rechazó tu oferta'
      });

      console.log(`✅ Oferta ${offerId} rechazada y conductor ${oferta.conductor_id} notificado`);

      return {
        oferta: {
          id: oferta.id,
          estado: 'rechazada',
          fecha_rechazo: new Date()
        },
        conductor: {
          id: oferta.conductor.id,
          nombre: oferta.conductor.nombre_completo
        },
        mensaje: 'Oferta rechazada exitosamente'
      };

    } catch (error) {
      console.error('❌ Error rechazando oferta:', error.message);
      throw error;
    }
  }



/**
 * ✅ ELIMINAR BÚSQUEDA ACTIVA - Elimina completamente las solicitudes pendientes
 */
async deleteActiveSearch(userId) {
        try {
            console.log(`🗑️ Eliminando búsqueda activa para usuario ${userId}`);
            
            // Buscar solicitudes pendientes del usuario
            const activeRides = await Viaje.findAll({
                where: {
                    usuario_id: userId,
                    estado: ['solicitado', 'ofertas_recibidas']
                }
            });

            if (activeRides.length === 0) {
                console.log(`ℹ️ No hay solicitudes activas para eliminar del usuario ${userId}`);
                return { deletedCount: 0 };
            }

            let deletedCount = 0;

            // Usar transacción para eliminar todo de forma atómica
            const transaction = await Viaje.sequelize.transaction();

            try {
                for (const viaje of activeRides) {
                    console.log(`🗑️ Eliminando viaje ${viaje.id} y sus ofertas...`);
                    
                    // Eliminar ofertas relacionadas primero
                    await OfertaViaje.destroy({
                        where: { viaje_id: viaje.id },
                        transaction
                    });

                    // Eliminar el viaje
                    await viaje.destroy({ transaction });
                    
                    // Notificar a conductores que tenían ofertas pendientes
                    try {
                        for (const viaje of activeRides) {
                                // Buscar conductores cercanos para cada viaje antes de eliminarlo
                                const nearbyDrivers = await locationService.findNearbyDrivers(
                                    viaje.origen_lat,
                                    viaje.origen_lng,
                                    this.SEARCH_RADIUS
                                );

                                // Notificar a cada conductor cercano
                                for (const driver of nearbyDrivers) {
                                    try {
                                        websocketServer.notifyDriver(driver.conductorId, 'ride:deleted_remove_from_dashboard', {
                                            viaje_id: viaje.id,
                                            mensaje: 'Esta solicitud fue eliminada por el usuario',
                                            action: 'remove_from_list',
                                            timestamp: new Date()
                                        });
                                    } catch (notificationError) {
                                        console.warn(`⚠️ Error notificando eliminación a conductor ${driver.conductorId}:`, notificationError.message);
                                    }
                                }
                                
                            }
                    } catch (notificationError) {
                        console.warn('⚠️ Error notificando cancelación:', notificationError.message);
                    }

                    deletedCount++;
                }

                await transaction.commit();
                console.log(`✅ Eliminadas ${deletedCount} solicitudes del usuario ${userId}`);

                return { deletedCount };

            } catch (transactionError) {
                await transaction.rollback();
                throw transactionError;
            }

        } catch (error) {
            console.error('❌ Error eliminando búsqueda activa:', error.message);
            throw error;
        }
    }






/**
* ✅ FUNCIONES AUXILIARES
*/

// verificamos que el usuario no tenga viajes activos
async checkUserActiveRides(userId){
        const activeRide = await Viaje.findOne({
            where: {
                usuario_id: userId,
                estado: ['solicitado', 'ofertas_recibidas', 'aceptado', 'en_curso']
            },
            order: [['fecha_solicitud', 'DESC']]
        });
        
        if (activeRide){
            console.log(`🚨 VIAJE ACTIVO ENCONTRADO para usuario ${userId}:`);
            console.log(`   - ID: ${activeRide.id}`);
            console.log(`   - Estado: ${activeRide.estado}`);
            console.log(`   - Fecha solicitud: ${activeRide.fecha_solicitud}`);
            console.log(`   - Origen: ${activeRide.origen_direccion || `${activeRide.origen_lat}, ${activeRide.origen_lng}`}`);
            console.log(`   - Destino: ${activeRide.destino_direccion || `${activeRide.destino_lat}, ${activeRide.destino_lng}`}`);
            
            // Calcular tiempo transcurrido
            const tiempoTranscurrido = new Date() - new Date(activeRide.fecha_solicitud);
            const minutosTranscurridos = Math.floor(tiempoTranscurrido / (1000 * 60));
            
            // ✅ LÓGICA MEJORADA DE AUTO-CANCELACIÓN - MÁS AGRESIVA
            let shouldCancel = false;
            let cancelReason = '';
            
            if (activeRide.estado === 'solicitado' && minutosTranscurridos > 3) {
                shouldCancel = true;
                cancelReason = `Auto-cancelado por timeout - ${minutosTranscurridos} minutos sin ofertas`;
            } else if (activeRide.estado === 'ofertas_recibidas' && minutosTranscurridos > 1) {
                shouldCancel = true;
                cancelReason = `Auto-cancelado - ${minutosTranscurridos} minutos sin aceptar ofertas`;
            } else if (minutosTranscurridos > 10) {
                // Cancelar cualquier viaje que tenga más de 10 minutos sin importar el estado
                shouldCancel = true;
                cancelReason = `Auto-cancelado por inactividad - ${minutosTranscurridos} minutos de antigüedad`;
            }
            
            if (shouldCancel) {
                console.log(`⏰ Auto-cancelando viaje ${activeRide.id} - ${cancelReason}`);
                
                await activeRide.update({
                    estado: 'cancelado',
                    motivo_cancelacion: cancelReason,
                    cancelado_por: 'sistema_timeout_auto',
                    fecha_cancelacion: new Date()
                });
                
                // Cancelar ofertas pendientes si las hay
                if (activeRide.estado === 'ofertas_recibidas') {
                    await OfertaViaje.update(
                        { 
                            estado: 'cancelada',
                            fecha_cancelacion: new Date()
                        },
                        {
                            where: {
                                viaje_id: activeRide.id,
                                estado: 'pendiente'
                            }
                        }
                    );
                }
                
                // Notificar al usuario
                try {
                    websocketServer.notifyUser(userId, 'ride:auto_cancelled', {
                        viaje_id: activeRide.id,
                        motivo: 'Viaje cancelado automáticamente por inactividad',
                        minutos_transcurridos: minutosTranscurridos,
                        puede_crear_nuevo: true
                    });
                } catch (notificationError) {
                    console.warn('⚠️ Error enviando notificación de auto-cancelación:', notificationError.message);
                }
                
                console.log(`✅ Viaje ${activeRide.id} auto-cancelado, usuario puede crear nuevo viaje`);
                return; // Permitir crear nuevo viaje
            }
            
            // Si el viaje no se puede auto-cancelar, lanzar error
            throw new ConflictError(`Ya tiene un viaje activo (${activeRide.estado}). ID: ${activeRide.id}. Complete o cancele el viaje actual antes de crear uno nuevo.`);
        }
}
     // calculando la tarifa base referencial
calculateBaseFare(distanceKm){
        const baseFare = 3.5; // S/, 3.50
        const perKm = 1.2; // 1.2 soles  por KM
        return Math.round((baseFare + (distanceKm * perKm)) * 100)/ 100;
     }

     // ✅ CALCULAR PRECIO SUGERIDO INTELIGENTE
calculateSuggestedPrice(distanceKm){
        const baseFare = 3.5; // S/, 3.50 base
        const perKm = 1.5; // S/, 1.50 por km (un poco más que la tarifa referencial)
        const demandMultiplier = 1.1; // 10% adicional por demanda
        
        const basePrice = baseFare + (distanceKm * perKm);
        const suggestedPrice = basePrice * demandMultiplier;
        
        // Redondear a 0.50 más cercano para precios más atractivos
        return Math.round(suggestedPrice * 2) / 2;
     }

     // calcular tiempo de llegada del conductor
calculateArrivalTime(conductorLat, conductorLng, origenLat, origenLng){
        const distance = calculateHaversineDistance(conductorLat, conductorLng, origenLat, origenLng);
        // con una velocidad de 25 km/h
        const timeHours = distance / 25;
        return Math.ceil(timeHours * 60);
}

setupRideTimeout(viajeId){
        console.log(`⏰ Configurando timeout de ${this.TIMEOUT_SECONDS} segundos para viaje ${viajeId}`);
        
        setTimeout(async() => {
            try {
                console.log(`🔍 Verificando timeout para viaje ${viajeId}...`);
                
                const viaje = await Viaje.findByPk(viajeId);
                
                if (!viaje) {
                    console.log(`⚠️ Viaje ${viajeId} no encontrado para timeout`);
                    return;
                }

                console.log(`📊 Estado actual del viaje ${viajeId}: ${viaje.estado}`);

                // ✅ SOLO CANCELAR SI SIGUE EN ESTADO 'solicitado'
                if (viaje.estado === 'solicitado') {
                    console.log(`⏰ TIMEOUT: Cancelando viaje ${viajeId} - sin ofertas recividas`);
                    
                    await viaje.update({
                        estado: 'cancelado',
                        motivo_cancelacion: 'Timeout - sin ofertas recividas en el tiempo límite',
                        cancelado_por: 'sistema_timeout',
                        fecha_cancelacion: new Date()
                    });

                    // Notificar al pasajero
                    websocketServer.notifyUser(viaje.usuario_id, 'ride:timeout', {
                        viaje_id: viajeId,
                        mensaje: `No se recibieron ofertas para tu viaje en ${this.TIMEOUT_SECONDS / 60} minutos. Intenta nuevamente o ajusta el precio.`,
                        sugerencia: 'Considera aumentar el precio sugerido para atraer más conductores',
                        timeout_segundos: this.TIMEOUT_SECONDS
                    });

                    // Push notification - DESHABILITADO TEMPORALMENTE
                    console.log(`🚫 Push notification de timeout omitida para usuario ${viaje.usuario_id} - Firebase deshabilitado`);
                    /*
                    try {
                        await firebaseService.sendToUser(viaje.usuario_id, {
                            title: '⏰ Viaje sin ofertas',
                            body: `No se encontraron conductores en ${this.TIMEOUT_SECONDS / 60} minutos`,
                            data: {
                                type: 'ride_timeout',
                                viaje_id: viajeId
                            }
                        });
                    } catch (pushError) {
                        console.warn('⚠️ Error enviando push notification de timeout:', pushError.message);
                    }
                    */

                    console.log(`✅ Viaje ${viajeId} cancelado por timeout`);
                    
                } else {
                    console.log(`✅ Viaje ${viajeId} ya tiene estado '${viaje.estado}' - no se cancela por timeout`);
                }

            } catch (error) {
                console.error(`❌ Error en timeout del viaje ${viajeId}:`, error.message);
            }
        }, this.TIMEOUT_SECONDS * 1000); // Convertir a milisegundos
    }




}

module.exports = new RidesService();
