const {Server} = require('socket.io');
const jwt = require('jsonwebtoken');
const {<PERSON><PERSON><PERSON>, Conductor, Viaje} = require('../models');

class WebSocketServer{
    constructor(){
        this.io = null;
        this.connectedUsers = new Map(); // key = userId y value = socketId
        this.connectedDrivers = new Map(); // key = conductorId y value : socketId
    }
 /**
   * ✅ INICIALIZAR WEBSOCKET SERVER
   */
  
 initialize(httpServer){
    try{
        console.log(' 🕸️ Inicializando websocker server...');;
        this.io = new Server(httpServer, {
            cors: {
                origin: process.env.FRONTEND_URL || "*",
                methods: ["GET", "POST"],
                credentials: true
            },
            transports: ['websocket', 'polling']
        });

        this.io.use(async (socket, next)=>{
            try{
                // Intentar obtener token de múltiples fuentes
                const token = socket.handshake.auth.token || 
                             socket.handshake.headers.authorization?.replace('Bearer ', '') ||
                             socket.handshake.query.token;
                
                console.log('🔍 Intentando autenticar WebSocket...');
                //console.log('📋 Auth data:', socket.handshake.auth);
                //console.log('📋 Headers:', socket.handshake.headers);
                //console.log('📋 Query:', socket.handshake.query);
                
                console.log('🔍 Intentando autenticar WebSocke... (autnticado)');
                if (!token){
                    console.log('❌ Token no proporcionado en WebSocket');
                    return next(new Error('Token no proporcionado'));
                }

                console.log('🔑 Token encontrado:', token.substring(0, 20) + '...');
                // ✅ TEMPORAL: Permitir tokens de testing para debug
                                if (token === 'valid_user_token_for_testing') {
                                    console.log('🧪 USANDO TOKEN DE TESTING - MODO DEBUG');
                                    socket.userId = '1'; // Usuario ID temporal para testing
                                    socket.userType = 'usuario';
                                    return next(); // Permitir conexión sin validación JWT
                                }
                const decoded = jwt.verify(token, process.env.JWT_SECRET);
                
                // Manejar diferentes tipos de tokens (usuario vs conductor)
                if (decoded.conductorId) {
                    // Token de conductor
                    socket.userId = decoded.conductorId;
                    socket.userType = 'conductor';
                } else if (decoded.userId) {
                    // Token de usuario
                    socket.userId = decoded.userId || decoded.id;
                    socket.userType = decoded.tipo ||decoded.type ||'usuario';
                } else {
                    throw new Error('Token no contiene ID válido');
                }
                
                console.log(`🔐 Cliente autenticado: ${socket.userType} ${socket.userId}`);
                next();

            }catch(error){
                 console.error('❌ Error autenticacion websocket:', error.message);
                 next(new Error('Token inválido'));
            }
        });

        // maneja las conexiones
        this.io.on('connection', (socket)=>{
            this.handleConnection(socket);
        });
        console.log(`[DEBUG] Salas del pasajero:`);
        console.log(' ✅ websocket server inicializando correctamente');
    }catch(error){
        console.error('❌ Error incializando WebSocket: ', error.message);
        throw error;
    }
 }

//  nueva conexion a websocket

handleConnection(socket){
    try{

        // crar los rooms para pasajero y conductor con id del usuario y id del websocket
          console.log(`✅ Nueva conexion: ${socket.userType} ${socket.userId}`);
            console.log(`   - Socket ID: ${socket.id}`);
            console.log(`   - User Type: ${socket.userType}`);
      if(socket.userType === 'usuario'   || socket.userType === 'pasajero' ){
    this.connectedUsers.set(socket.userId, socket.id);
    socket.join(`user_${socket.userId}`);
    
    // ✅ NUEVO: Debug extensivo y registro consistente
    console.log(`✅ Usuario registrado en mapa:`, {
        userId: socket.userId,
        socketId: socket.id,
        room: `user_${socket.userId}`,
        mapSize: this.connectedUsers.size,
        allUsers: Array.from(this.connectedUsers.entries())
    });
    
    // ✅ NUEVO: Escuchar evento check-in
    socket.on('passenger:check_in', (data) => {
        console.log(`✅ PASAJERO CHECK-IN: ${socket.userId}`);
        // Re-registrar por si acaso
        this.connectedUsers.set(socket.userId, socket.id); 
        
        // Confirmar al cliente
        socket.emit('passenger:check_in_confirmed', {
            success: true,
            userId: socket.userId,
            socketId: socket.id
        });
    });
 
        }else if(socket.userType === 'conductor'){
            this.connectedDrivers.set(socket.userId, socket.id);
            socket.join(`driver_${socket.userId}`);
        }

        // TODOS los eventos necesarios para pasajero
        if(socket.userType === 'usuario' || socket.userType === 'pasajero' ){
            socket.on('ride:request', (data)=> this.handleRideRequest(socket, data));
            socket.on('ride:accept_offer', (data)=> this.handleAcceptOffer(socket, data));
            socket.on('ride:reject_offer', (data)=> this.handleRejectOffer(socket, data));
            socket.on('ride:counter_offer', (data)=> this.handleCounterOffer(socket, data));
            socket.on('ride:cancelled', (data)=> this.handleCancelRide(socket, data));
            console.log(`[DEBUG] Salas del pasajero (${socket.userId}):`, Array.from(socket.rooms));
        }
        
        // TODOS los evento para conductores
        if(socket.userType === 'conductor'){
            socket.on('ride:offer', (data) => this.handleDriverOffer(socket, data));
            socket.on('ride:accept_counter', (data)=> this.handleAcceptCounter(socket, data));
            socket.on('ride:accept_original_price', (data) => this.handleAcceptOriginalPrice(socket, data) );
            socket.on('location:update', (data)=> this.handleLocationUpdate(socket, data));
            socket.on('location:update', (data)=> this.handleLocationUpdate(socket, data));
        }

        // Eventos generales

        socket.on('disconnect', ()=> this.handleDisconnection(socket) );
        socket.on('ping', ()=> socket.emit('pong'));
        
    }catch(error){
        console.error(' ❌ Error manejando conexion: ', error.message);
        socket.disconnect();
        
    }
}

// maneja la desconexiòn 

handleDisconnection(socket){
    console.log(`💀 Desconexion: ${socket.userType} ${socket.userId}`);
    if(socket.userType === 'usuario'){
                // ✅ ADD DETAILED LOGGING HERE
        const wasConnected = this.connectedUsers.has(socket.userId);
        console.log(`--- USUARIO DESCONECTADO ---`);
        console.log(`   - UserID: ${socket.userId}`);
        console.log(`   - SocketID: ${socket.id}`);
        console.log(`   - Estaba en el mapa?: ${wasConnected}`);
        this.connectedUsers.delete(socket.userId);
        console.log(`   - Removido de connectedUsers.`);
        console.log(`   - Tamaño del mapa ahora: ${this.connectedUsers.size}`);
        console.log(`--------------------------`);
    }else if(socket.userType === 'conductor'){
        this.connectedDrivers.delete(socket.userId);
    }
}

// eventos del viaje (IB)

handleRideRequest(socket, data){
    console.log(`Solicitu de viaje via websocket: `, data);
    socket.emit('ride:request_received', {success: true, data});
}

// ✅ MÉTODO CORREGIDO
async handleDriverOffer(socket, data){
    try {
        console.log(`📤 Oferta de conductor via WebSocket:`, data);
        console.log(`   - Viaje ID: ${data.viaje_id}`);
        console.log(`   - Conductor: ${socket.userId}`);
        console.log(`   - UserId del frontend: ${data.userId}`);
        
        // ✅ OBTENER EL USER ID DESDE LA BASE DE DATOS
        const viaje = await this.getViajeWithUser(data.viaje_id);
        
        if (!viaje || !viaje.usuario_id) {
            console.error(`❌ No se encontró viaje o usuario para viaje ${data.viaje_id}`);
            return;
        }
        
        const userId = viaje.usuario_id;
        console.log(`✅ Usuario del viaje encontrado: ${userId}`);
        
        // ✅ PREPARAR DATOS COMPLETOS DE LA OFERTA
        const offerData = {
            oferta_id: data.oferta_id || `offer_${Date.now()}`,
            viaje_id: data.viaje_id,
            conductor_id: socket.userId,
            tarifa_propuesta: data.tarifa_propuesta,
            mensaje: data.mensaje || 'Oferta del conductor',
            timestamp: new Date().toISOString(),
            event_type: 'ride:offer_received',
            
            // ✅ INFORMACIÓN DEL CONDUCTOR (si está disponible)
            conductor_nombre: socket.conductorNombre || 'Conductor',
            conductor_rating: socket.conductorRating || 5.0,
            
            // ✅ INFORMACIÓN DEL VIAJE
            viaje: {
                id: viaje.id,
                origen: viaje.origen,
                destino: viaje.destino,
                precio_original: viaje.precio_sugerido
            }
        };
        
        // ✅ NOTIFICAR AL PASAJERO CON DATOS COMPLETOS
        this.notifyUser(userId, 'ride:offer_received', offerData);
        
        console.log(`✅ Oferta enviada al usuario ${userId}`);
        
    } catch (error) {
        console.error(`❌ Error procesando oferta del conductor:`, error.message);
    }
}

// ✅ MÉTODO HELPER PARA OBTENER VIAJE CON USUARIO
async getViajeWithUser(viajeId) {
  try {
    // Importa el modelo directamente aquí si no está disponible globalmente
    
    // Buscar el viaje por ID
    const viaje = await Viaje.findByPk(viajeId);
    
    if (!viaje) {
      console.log(`❌ No se encontró viaje con ID: ${viajeId}`);
      return null;
    }
    
    console.log(`✅ Viaje encontrado: ${viajeId}`);
    console.log(`   - Usuario ID: ${viaje.usuario_id}`);
    
    return viaje;
  } catch (error) {
    console.error(`❌ Error obteniendo viaje ${viajeId}: ${error.message}`);
    return null;
  }
}

handleAcceptOffer(socket, data){
    console.log(`Oferta aceptada via websocket: `, data);
    // notifcar al conductor
    this.notifyDriver(data.conductorId, 'ride:offer_accepted', data);
}

handleAcceptOriginalPrice(socket, data){
    try {
        console.log(` 🚕 Conductor ${socket.userId} acepta precio original via websocket: `, data);

        // validamos los datos requerido
        if(!data.viajeId){
            socket.emit('error', {message: 'ID de viaje requerido'});
            return
        }

        // emitimos confirmacion al conductor
        socket.emit('ride:original_price_acceptance_received', {
            success: true,
            viajeId: data.viajeId,
            message: 'Aceptaacion de precio orginal recivida'
        })

        
    } catch (error) {
        console.error(`❌ Error manjando aceptacion precio orinal`, error.message);
        socket.emit('error', {
            message: 'Error procesando aceptaciòn de precio original',
            error: error.message
        });
         
    }
}

handleLocationUpdate(socket, data){
    console.log(` Actualizacion de ubicacoin conductor ${socket.userId}`);
}

 /**
   *  MÉTODOS DE NOTIFICACIÓN MEJORADOS
   */
// En notifyUser:
// Modificar notifyUser para incluir notificación directa
notifyUser(userId, event, data){
    try {
        console.log(`📣 Intentando notificar a usuario ${userId} - Evento: ${event}`);
        
        // 1. Obtener socketId directamente
        const socketId = this.connectedUsers.get(userId);
        console.log(`   - SocketId en mapa: ${socketId || 'NO ENCONTRADO'}`);
        
        // 2. Obtener room
        const room = `user_${userId}`;
        console.log(`   - Room objetivo: ${room}`);
        
        // 3. Verificar si usuario está conectado
        console.log(`   - Usuarios conectados: [${Array.from(this.connectedUsers.keys()).join(', ')}]`);
        console.log(`   - Usuario está en mapa: ${this.connectedUsers.has(userId) ? 'SÍ' : 'NO'}`);
        
        // Preparar datos
        const enrichedData = {
            ...data,
            timestamp: new Date().toISOString(),
            event_type: event,
            user_id: userId,
            debug_info: "Notificación enviada"
        };
        
        // 4. AMBOS MÉTODOS: Notificar por room Y por socketId directo
        if (socketId) {
            console.log(`   - Notificando directamente a socket: ${socketId}`);
            this.io.to(socketId).emit(event, {
                ...enrichedData,
                debug_info: "Notificación directa por socketId"
            });
        }
        
        console.log(`   - Notificando a room: ${room}`);
        this.io.to(room).emit(event, enrichedData);
        
        console.log(`✅ Notificación enviada al usuario ${userId}`);
    } catch (error) {
        console.error(`❌ Error notificando usuario ${userId}: ${error.message}`);
    }
}
  
  // notificar al conductor con información enriquecida
  notifyDriver(conductorId, event, data){
    try {
        const room = `driver_${conductorId}`;
        
        // ✅ AGREGAR TIMESTAMP Y METADATA PARA CONDUCTORES
        const enrichedData = {
            ...data,
            timestamp: new Date().toISOString(),
            event_type: event,
            conductor_id: conductorId,
            // ✅ INFORMACIÓN ADICIONAL PARA SOLICITUDES
            ...(event === 'ride:new_request' && {
                notification_priority: 'high',
                requires_response: true,
                expires_at: new Date(Date.now() + (data.timeout_segundos * 1000)).toISOString()
            })
        };
        
        this.io.to(room).emit(event, enrichedData);
        console.log(`🚗 Notificación por webScoket enviada a conductor ${conductorId}: ${event}`); 
    } catch (error) {
        console.error(`❌ Error notificando al conductor ${conductorId}: `, error.message);
    }
  }

  // ✅ NUEVO MÉTODO PARA NOTIFICAR SOLICITUD CON INFORMACIÓN COMPLETA
  notifyDriverNewRequest(conductorId, requestData){
    try {
        const room = `driver_${conductorId}`;
        
        // ✅ ESTRUCTURA COMPLETA PARA EL CONDUCTOR
        const completeRequestData = {
            event_type: 'ride:new_request',
            timestamp: new Date().toISOString(),
            conductor_id: conductorId,
            notification_priority: 'high',
            requires_response: true,
            expires_at: new Date(Date.now() + (requestData.timeout_segundos * 1000)).toISOString(),
            
            // ✅ DATOS DEL VIAJE
            viaje: {
                id: requestData.viaje_id,
                origen: requestData.origen,
                destino: requestData.destino,
                distancia_km: requestData.distancia_km,
                tiempo_estimado: requestData.tiempo_estimado,
                distancia_conductor: requestData.distancia_conductor
            },
            
            // ✅ INFORMACIÓN DEL USUARIO (si está disponible)
            usuario: requestData.usuario || {
                nombre: 'Usuario',
                foto: null,
                rating: 0.0
            },
            
            // ✅ INFORMACIÓN DE PRECIOS
            precios: {
                usuario_pide: requestData.precio_sugerido,
                app_sugiere: requestData.precio_sugerido_app || requestData.precio_sugerido,
                moneda: 'PEN'
            },
            
            // ✅ MÉTODOS DE PAGO
            metodos_pago: requestData.metodos_pago || [],
            
            // ✅ CONFIGURACIÓN DE TIMEOUT
            timeout_segundos: requestData.timeout_segundos,
            
            // ✅ ACCIONES DISPONIBLES
            acciones: {
                puede_ofertar: true,
                tiempo_para_ofertar: requestData.timeout_segundos
            }
        };
        
        this.io.to(room).emit('ride:new_request', completeRequestData);
        console.log(`🎯 Solicitud completa enviada a conductor ${conductorId}`);
        
    } catch (error) {
        console.error(`❌ Error enviando solicitud completa al conductor ${conductorId}: `, error.message);
    }
  }

  notifyMultipleDrivers(conductorIds, event, data){
    try {
        conductorIds.forEach(conductorId =>{
            this.notifyDriver(conductorId, event, data);
        });
        console.log(`📡 Notificación broadcast a ${conductorIds.length} conductores: ${event}`);
    } catch (error) {
        console.error(' ❌ Error en notificación múltiple', error.message);
    }
  }
  


// Método para notificar cancelación que requiere actualización de dashboard
notifyRideCancelledForDashboard(conductorIds, data) {
    try {
        if (Array.isArray(conductorIds)) {
            conductorIds.forEach(conductorId => {
                this.notifyDriver(conductorId, 'ride:cancelled_update_dashboard', {
                    ...data,
                    action: 'remove_from_list',
                    timestamp: new Date()
                });
            });
        }
        console.log(`📢 ${conductorIds.length} conductores notificados de cancelación para actualizar dashboard`);
    } catch (error) {
        console.error('❌ Error notificando cancelación para dashboard:', error.message);
    }
}

// Método para notificar eliminación de solicitudes
notifyRideDeletedForDashboard(conductorIds, data) {
    try {
        if (Array.isArray(conductorIds)) {
            conductorIds.forEach(conductorId => {
                this.notifyDriver(conductorId, 'ride:deleted_update_dashboard', {
                    ...data,
                    action: 'remove_from_list',
                    timestamp: new Date()
                });
            });
        }
        console.log(`📢 ${conductorIds.length} conductores notificados de eliminación para actualizar dashboard`);
    } catch (error) {
        console.error('❌ Error notificando eliminación para dashboard:', error.message);
    }
}

/**
 * ✅ NOTIFICAR AL PASAJERO QUE SU PRECIO ORIGINAL FUE ACEPTADO
 */
notifyPriceAcceptedImmediately(userId, data) {
    try {
        console.log(`🎉 Notificando precio original aceptado a usuario ${userId}`);
        
        const enrichedData = {
            ...data,
            timestamp: new Date().toISOString(),
            event_type: 'price_accepted_immediately',
            user_id: userId
        };
        
        // Notificar al pasajero que su precio fue aceptado
        this.notifyUser(userId, 'ride:price_accepted_immediately', enrichedData);
        
        console.log(`✅ Pasajero ${userId} notificado de precio aceptado`);
    } catch (error) {
        console.error('❌ Error notificando precio aceptado:', error.message);
    }
}

  // estadistica adicionales

  getStats(){
    return{
        connectedUsers: this.connectedUsers.size,
        connectedDrivers: this.connectedDrivers.size,
        totalConectios: this.connectedUsers.size + this.connectedDrivers.size,
        timestamp: new Date()
    };
  }

  getIO(){
    return this.io;
  }
  isUserConnected(userId){
    return this.connectedUsers.has(userId);
  }

  isDriverConnected(conductorId){
    return this.connectedDrivers.has(conductorId);
  }
}

module.exports = new WebSocketServer()
