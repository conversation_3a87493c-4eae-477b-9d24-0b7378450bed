require('dotenv').config();

module.exports = {
  development: {
    username: process.env.DB_USER || 'dev',
    password: process.env.DB_PASSWORD || 'secret',
    database: process.env.DB_NAME || 'joya',
    host: process.env.DB_HOST || 'postgres',
    port: parseInt(process.env.DB_PORT) || 5432,
    dialect: 'postgres',
    logging: false, // Para ver queries en desarrollo
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000
    }
  },
  test: {
    username: process.env.DB_USER || 'dev',
    password: process.env.DB_PASSWORD || 'secret',
    database: (process.env.DB_NAME || 'joya') + '_test',
    host: process.env.DB_HOST || 'postgres',
    port: parseInt(process.env.DB_PORT) || 5432,
    dialect: 'postgres',
    logging: false
  },
  production: {
    username: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT),
    dialect: 'postgres',
    logging: false,
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000
    }
  }
};